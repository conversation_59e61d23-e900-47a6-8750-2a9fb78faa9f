# Kustomize Migration Task List

## Overview
This document outlines the comprehensive task list for migrating from the current custom Mustache/Handlebars template engine to Kustomize with multi-environment support while maintaining all existing GitOps functionality.

## Current State Analysis
- **Template Engine**: Custom Handlebars/Mustache processing in PowerShell and Python scripts
- **Template Variables**: `{{PROJECT_ID}}`, `{{NAMESPACE}}`, `{{REPLICAS}}`, `{{APP_TYPE}}`, `{{ENVIRONMENT}}`, etc.
- **Processing Scripts**: `generate-manifests-cicd.ps1`, `generate-manifests-cicd.py`, `generate-manifests.ps1`
- **Template Files**: Located in `templates/k8s/` and `templates/argocd/`
- **GitHub Actions**: Deployment is triggered by a `workflow_dispatch` event, enabling manual or issue-driven deployments with template processing
- **ArgoCD Integration**: Project-based application management

## Migration Task Breakdown

1. **Remove GitHub Issue Template Dependencies**: 
- Eliminate all references to GitHub issue-driven deployments and workflow triggers
- Remove tasks related to issue template processing and issue-based automation
- Focus solely on direct CI/CD pipeline deployments using workflow_dispatch events
- Remove any mention of issue parsing, issue labels, or issue body processing

2. **Delete Unnecessary PowerShell Scripts**:
- Remove all PowerShell-related tasks and script references if they are not essential for the core Kustomize migration
- Specifically evaluate and remove: `generate-manifests-cicd.ps1`, `generate-manifests.ps1`, and any other PowerShell scripts
- Keep only Python scripts (`generate-manifests-cicd.py`) and shell scripts that are required for the migration
- Update all task references to reflect the removal of PowerShell dependencies

### Phase 1: Repository Structure Setup
- [ ] **Task 1.1**: Create base Kustomize directory structure
  - Create `manifests/base/` directory
  - Create `manifests/overlays/dev/`, `manifests/overlays/staging/`, `manifests/overlays/production/`
  - Create `manifests/argocd/` directory for ArgoCD project configuration

- [ ] **Task 1.2**: Analyze current template variables and their usage patterns
  - Document all template variables used across templates
  - Map template conditionals (`{{#if}}`, `{{#eq}}`) to Kustomize equivalents
  - Identify environment-specific configurations

### Phase 2: Base Manifest Creation
- [ ] **Task 2.1**: Convert deployment template to base Kustomize manifest
  - Remove all template variables from `templates/k8s/deployment.yaml`
  - Create `manifests/base/deployment.yaml` with default values
  - Handle complex conditionals (database init containers, app-type specific configs)

- [ ] **Task 2.2**: Convert service template to base manifest
  - Transform `templates/k8s/service.yaml` to `manifests/base/service.yaml`
  - Remove template variables, use default port 80

- [ ] **Task 2.3**: Convert configmap template to base manifest
  - Transform `templates/k8s/configmap.yaml` to `manifests/base/configmap.yaml`
  - Handle DigitalOcean PostgreSQL configuration
  - Remove environment-specific conditionals

- [ ] **Task 2.4**: Create base kustomization.yaml
  - List all base resources (deployment, service, configmap)
  - Define common labels and annotations
  - Set up namespace field

### Phase 3: Environment Overlays Creation
- [ ] **Task 3.1**: Create development environment overlay
  - `manifests/overlays/dev/kustomization.yaml` with namespace: dev
  - `manifests/overlays/dev/patch-replicas.yaml` for 1 replica, 250m CPU, 256Mi memory
  - Environment-specific labels and annotations

- [ ] **Task 3.2**: Create staging environment overlay
  - `manifests/overlays/staging/kustomization.yaml` with namespace: staging
  - `manifests/overlays/staging/patch-replicas.yaml` for 2 replicas, 500m CPU, 512Mi memory
  - Staging-specific configurations

- [ ] **Task 3.3**: Create production environment overlay
  - `manifests/overlays/production/kustomization.yaml` with namespace: production
  - `manifests/overlays/production/patch-replicas.yaml` for 3 replicas, 1000m CPU, 1024Mi memory
  - Production-specific configurations and security settings

### Phase 4: ArgoCD Project and Application Configuration
- [ ] **Task 4.1**: Create ArgoCD Project manifest
  - `manifests/argocd/argocd-project.yaml` with multi-cluster destination support
  - Define source repository permissions
  - Set up RBAC for project access

- [ ] **Task 4.2**: Create environment-specific ArgoCD Applications
  - `manifests/overlays/dev/application.yaml` referencing dev overlay
  - `manifests/overlays/staging/application.yaml` referencing staging overlay
  - `manifests/overlays/production/application.yaml` referencing production overlay
  - Each application references the ArgoCD project

### Phase 5: Script Migration and CI/CD Updates
- [ ] **Task 5.1**: Update PowerShell generation script
  - Modify `scripts/generate-manifests-cicd.ps1` to use `kubectl apply -k`
  - Remove template processing logic
  - Add Kustomize build and validation

- [ ] **Task 5.2**: Update Python generation script
  - Modify `scripts/generate-manifests-cicd.py` to use Kustomize
  - Remove Handlebars processing functions
  - Implement Kustomize overlay selection based on environment

- [ ] **Task 5.3**: Update GitHub Actions workflow
  - Modify `.github/workflows/generate-app-manifests.yaml`
  - Replace template processing with Kustomize commands
  - Ensure ArgoCD project is applied before applications

### Phase 6: Database Integration and Secrets Management
- [ ] **Task 6.1**: Configure DigitalOcean PostgreSQL in base ConfigMap
  - Set DB_HOST to `myapp-postgres.dbs.digitalocean.com`
  - Configure DB_PORT, DB_NAME in base configuration
  - Remove template conditionals for database settings

- [ ] **Task 6.2**: Handle secrets management transition
  - Document external secret management requirements
  - Ensure existing secret references work with Kustomize
  - Maintain backward compatibility for secret naming

### Phase 7: Validation and Testing
- [ ] **Task 7.1**: Implement Kustomize validation
  - Add `kubectl kustomize` validation to scripts
  - Test all environment overlays build successfully
  - Validate generated YAML syntax

- [ ] **Task 7.2**: Test ArgoCD integration
  - Verify ArgoCD project creation
  - Test application sync with new Kustomize structure
  - Ensure automated sync policies work correctly

- [ ] **Task 7.3**: Cross-cluster deployment testing
  - Test staging and production cluster targeting
  - Verify environment-specific configurations apply correctly
  - Test GitHub Actions dispatch triggers

### Phase 8: Documentation and Migration Guide
- [ ] **Task 8.1**: Create deployment documentation
  - Document new Kustomize-based deployment process
  - Provide environment management instructions
  - Include troubleshooting guide

- [ ] **Task 8.2**: Create migration guide for existing applications
  - Document steps to migrate existing applications
  - Provide rollback procedures
  - Include validation checklist

### Phase 9: Backward Compatibility and Rollback
- [ ] **Task 9.1**: Implement parallel deployment support
  - Maintain existing template system during transition
  - Allow gradual migration of applications
  - Implement feature flags for template vs Kustomize

- [ ] **Task 9.2**: Create rollback procedures
  - Document steps to revert to template system
  - Maintain template backups
  - Test rollback scenarios

### Phase 10: Cleanup and Optimization
- [ ] **Task 10.1**: Remove deprecated template files
  - Archive old template system files
  - Clean up unused template processing code
  - Update repository documentation

- [ ] **Task 10.2**: Optimize Kustomize structure
  - Review and optimize overlay configurations
  - Implement common patches for reusability
  - Add advanced Kustomize features as needed

## Success Criteria
- [ ] All environment overlays build successfully with `kubectl kustomize`
- [ ] ArgoCD applications sync without errors
- [ ] GitHub Actions workflow deploys using Kustomize
- [ ] DigitalOcean PostgreSQL connectivity maintained
- [ ] Cross-cluster deployments work correctly
- [ ] Zero disruption to existing applications during migration
- [ ] Complete documentation and migration guides available

## Risk Mitigation
- Maintain parallel systems during transition
- Implement comprehensive testing at each phase
- Create detailed rollback procedures
- Validate all changes in development environment first
- Maintain backward compatibility throughout migration

## Dependencies
- Kubernetes 1.21+ cluster access
- ArgoCD installation and configuration
- GitHub Actions runner with kubectl and Kustomize
- DigitalOcean PostgreSQL database access
- Repository write permissions for manifest updates
